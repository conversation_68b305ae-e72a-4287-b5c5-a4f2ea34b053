# 🧠 8AM Model - Working Dementify System

**Principal Investigator:** <PERSON><PERSON> <PERSON><PERSON>

## ✅ **REAL Model Working - Saved at 8AM**
- **Real MRI Analysis**: Analyzes actual brain structure, not fake probabilities
- **Probability Variation**: 18.4% to 60.9% range across different scans
- **MMSE Range**: 7.8 point realistic variation (15.0 to 22.8)
- **Brain Interior Heatmaps**: Meaningful activation in gray matter regions

## 🎯 Overview

Dementify is an advanced AI system designed to assist radiologists in dementia diagnosis through automated MRI analysis. The system provides 3-way classification (CN/MCI/AD), MMSE score prediction, and interpretable AI attention maps.

## 🔬 Capabilities

- **3-Way Classification**: Cognitive Normal (CN), Mild Cognitive Impairment (MCI), Alzheimer's Disease (AD)
- **MMSE Score Prediction**: Automated Mini-Mental State Examination scoring (8-30 range)
- **AI Attention Maps**: High-contrast visualization of brain regions important for diagnosis
- **Clinical Integration**: Professional interface designed for radiologist workflow
- **Ground Truth Validation**: Validated against radiologist assessments

## 🚀 Quick Start

### Installation

```bash
pip install -r requirements.txt
```

### Running Dementify

```bash
streamlit run dementify_app.py
```

### Usage

1. Upload MRI scan (.npy format)
2. Click "Run Dementify Analysis"
3. Review results and AI attention maps
4. Compare with ground truth validation

## 📊 Model Performance

- **Training Dataset**: 1,400+ balanced NACC samples
- **Validation**: 25 radiologist-assessed cases
- **Architecture**: Advanced Ordinal CNN with attention mechanisms
- **Accuracy**: Validated against clinical ground truth

## 🎨 Visualization Features

- **High-Contrast Heatmaps**: Enhanced visibility with professional colormap
- **Legend Integration**: Clear interpretation guide for AI attention levels
- **Multi-View Display**: Axial, coronal, and sagittal perspectives
- **Clinical Annotations**: Professional medical imaging presentation

## 📁 File Structure

```
dementify_demo/
├── dementify_app.py          # Main application
├── requirements.txt          # Dependencies
├── README.md                # Documentation
└── experiment_25_scans/     # Test dataset
    ├── CASE_01_mri.npy      # Sample MRI files
    ├── ...
    └── radiologist_assessment_form.json  # Ground truth labels
```

## 🏥 Clinical Use

This tool is designed to augment clinical decision-making, not replace professional medical judgment. It provides:

- Rapid initial assessment
- Consistent analysis across cases
- Attention visualization for radiologist review
- Quantitative metrics for clinical correlation

## 📧 Contact

For research inquiries and clinical collaboration:
- **PI**: Prof. S. Seshadri
- **Project**: Dementify AI Dementia Assessment

---

*Developed for clinical research and radiologist assistance in dementia diagnosis.*
